# TLS配置指南

## 概述

鱼缸传感器项目现在支持自动TLS配置。当MQTT端口设置为8883时，系统会自动启用TLS加密连接。

## 功能特性

### 1. 自动TLS检测
- **端口8883**: 自动启用TLS（MQTT over SSL/TLS标准端口）
- **其他端口**: 使用标准MQTT连接

### 2. 证书配置
系统使用以下格式的证书：

```
-----BEGIN CERTIFICATE-----
MIIDrzCCApegAwIBAgIQCDvgVpBCRrGhdWrJWZHHSjANBgkqhkiG9w0BAQUFADBh
MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3
d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD
QTAeFw0wNjExMTAwMDAwMDBaFw0zMTExMTAwMDAwMDBaMGExCzAJBgNVBAYTAlVT
MRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j
b20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENBMIIBIjANBgkqhkiG
9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4jvhEXLeqKTTo1eqUKKPC3eQyaKl7hLOllsB
CSDMAZOnTjC3U/dDxGkAV53ijSLdhwZAAIEJzs4bg7/fzTtxRuLWZscFs3YnFo97
nh6Vfe63SKMI2tavegw5BmV/Sl0fvBf4q77uKNd0f3p4mVmFaG5cIzJLv07A6Fpt
43C/dxC//AH2hdmoRBBYMql1GNXRor5H4idq9Joz+EkIYIvUX7Q6hL+hqkpMfT7P
T19sdl6gSzeRntwi5m3OFBqOasv+zbMUZBfHWymeMr/y7vrTC0LUq7dBMtoM1O/4
gdW7jVg/tRvoSSiicNoxBN33shbyTApOB6jtSj1etX+jkMOvJwIDAQABo2MwYTAO
BgNVHQ8BAf8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUA95QNVbR
TLtm8KPiGxvDl7I90VUwHwYDVR0jBBgwFoAUA95QNVbRTLtm8KPiGxvDl7I90VUw
DQYJKoZIhvcNAQEFBQADggEBAMucN6pIExIK+t1EnE9SsPTfrgT1eXkIoyQY/Esr
hMAtudXH/vTBH1jLuG2cenTnmCmrEbXjcKChzUyImZOMkXDiqw8cvpOp/2PV5Adg
06O/nVsJ8dWO41P0jmP6P6fbtGbfYmbW0W5BjfIttep3Sp+dWOIrWcBAI+0tKIJF
PnlUkiaY4IBIqDfv8NZ5YBberOgOzW6sRBc4L0na4UU+Krk2U886UAb3LujEV0ls
YSEY1QSteDwsOoBrp+uvFRTp2InBuThs4pFsiv9kuXclVzDAGySj4dzp30d8tbQk
CAUw7C29C79Fv1C5qfPrmAESrciIxpg0X40KPMbp1ZWVbd4=
-----END CERTIFICATE-----
```

### 3. TLS配置参数
- **握手超时**: 30秒
- **连接超时**: 10秒
- **SNI支持**: 启用服务器名称指示

## 配置方法

### 1. 在Config.h中设置
```cpp
#define MQTT_SERVER "qe13a680.ala.cn-hangzhou.emqxsl.cn"
#define MQTT_PORT 8883  // 使用8883端口自动启用TLS
```

### 2. 初始化连接管理器
```cpp
ConnectivityManager connectivity;
connectivity.begin(MQTT_SERVER, MQTT_PORT);
```

### 3. 连接MQTT
```cpp
if (connectivity.connectMQTT(30000)) {
    Serial.println("TLS连接成功！");
}
```

## 工作流程

1. **初始化阶段**:
   - 检测MQTT端口
   - 如果端口为8883，设置`useTLS = true`
   - 配置WiFiClientSecure客户端

2. **TLS设置阶段**:
   - 加载CA证书
   - 配置超时参数
   - 启用SNI支持

3. **连接阶段**:
   - 建立WiFi连接
   - 进行TLS握手
   - 建立安全MQTT连接

## 日志输出示例

```
ConnectivityManager initialized for device: fish-tank-sensor-1-xxxxxxxxxxxx
MQTT Server: qe13a680.ala.cn-hangzhou.emqxsl.cn:8883 (TLS: enabled)
WiFi failure count: 0

Setting up TLS for secure MQTT connection...
TLS configuration completed with custom certificate

Connecting to MQTT broker: qe13a680.ala.cn-hangzhou.emqxsl.cn:8883 (TLS: enabled)
MQTT connected successfully!
Connection secured with TLS
```

## 证书更新

如需更新证书，请修改`ConnectivityManager.cpp`中的`setupTLS()`方法：

```cpp
const char* ca_cert = \
"-----BEGIN CERTIFICATE-----\n" \
"您的新证书内容\n" \
"-----END CERTIFICATE-----\n";
```

## 故障排除

### 1. TLS握手失败
- 检查证书是否正确
- 确认服务器支持TLS 1.2
- 验证时间同步

### 2. 连接超时
- 增加握手超时时间
- 检查网络连接质量
- 确认防火墙设置

### 3. 证书验证失败
- 确认证书格式正确
- 检查证书是否过期
- 验证证书链完整性

## 安全注意事项

1. **证书管理**: 定期更新CA证书
2. **时间同步**: 确保设备时间准确
3. **密钥安全**: 保护私钥不被泄露
4. **版本控制**: 使用最新的TLS版本
