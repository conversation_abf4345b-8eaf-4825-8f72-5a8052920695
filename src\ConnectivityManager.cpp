#include "ConnectivityManager.h"
#include "esp_crt_bundle.h"

// Static member definitions
const char *ConnectivityManager::BLUFI_POP = "abcd123";
const char *ConnectivityManager::BLUFI_SERVICE_NAME = "PROV_FishTank_SENSOR";
const char *ConnectivityManager::PREF_NAMESPACE = "connectivity";
const char *ConnectivityManager::PREF_WIFI_FAILURE_COUNT = "wifi_failures";
ConnectivityManager *ConnectivityManager::instance = nullptr;

ConnectivityManager::ConnectivityManager() : mqttClient(wifiClient),
                                             mqttPort(1883),
                                             useTLS(false),
                                             initialized(false),
                                             wifiConnected(false),
                                             mqttConnected(false),
                                             provisioningActive(false),
                                             wifiFailureCount(0)
{
    instance = this;
}

ConnectivityManager::~ConnectivityManager()
{
    disconnect();
    preferences.end();
}

bool ConnectivityManager::begin(const char *mqttServer, int mqttPort)
{

    this->mqttServer = mqttServer;
    this->mqttPort = mqttPort;

    // Detect if TLS should be used (port 8883 is standard MQTT over TLS)
    useTLS = (mqttPort == 8883);

    // Initialize preferences
    preferences.begin(PREF_NAMESPACE, false);
    loadWiFiFailureCount();

    generateDeviceId();
    setupTopics();
    this->mqttUser = deviceId;
    this->mqttPassword = "123";

    // Setup TLS if needed
    if (useTLS)
    {
        setupTLS();
        // Reinitialize MQTT client with secure client
        mqttClient.~PubSubClient();
        new (&mqttClient) PubSubClient(wifiClientSecure);
    }

    // Setup MQTT client
    mqttClient.setServer(mqttServer, mqttPort);
    mqttClient.setCallback(mqttCallback);

    // Setup WiFi event handler
    WiFi.onEvent(bluFiEventHandler);

    initialized = true;

    Serial.printf("ConnectivityManager initialized for device: %s\n", deviceId.c_str());
    Serial.printf("MQTT Server: %s:%d (TLS: %s)\n", mqttServer, mqttPort, useTLS ? "enabled" : "disabled");
    Serial.printf("WiFi failure count: %d\n", wifiFailureCount);

    return true;
}

bool ConnectivityManager::startBluFiProvisioning(uint32_t timeoutMs)
{
    if (!initialized)
    {
        Serial.println("ConnectivityManager not initialized!");
        return false;
    }

    Serial.println("Starting BluFi provisioning...");
    Serial.printf("Service name: %s\n", BLUFI_SERVICE_NAME);
    Serial.printf("Proof of possession: %s\n", BLUFI_POP);
    Serial.printf("Timeout: %lu ms\n", timeoutMs);

    provisioningActive = true;

    // Generate UUID for BluFi
    uint8_t uuid[16] = {0xb4, 0xdf, 0x5a, 0x1c, 0x3f, 0x6b, 0xf4, 0xbf,
                        0xea, 0x4a, 0x82, 0x03, 0x04, 0x90, 0x1a, 0x02};

    // Start BluFi provisioning
    WiFiProv.beginProvision(
        WIFI_PROV_SCHEME_BLE,
        WIFI_PROV_SCHEME_HANDLER_FREE_BLE,
        WIFI_PROV_SECURITY_1,
        BLUFI_POP,
        BLUFI_SERVICE_NAME,
        NULL, // service_key not needed for BLE
        uuid,
        true // reset_provisioned
    );

    // Print QR code for easy connection
    WiFiProv.printQR(BLUFI_SERVICE_NAME, BLUFI_POP, "ble");

    // Wait for provisioning to complete or timeout
    uint32_t startTime = millis();
    while (provisioningActive && (millis() - startTime) < timeoutMs)
    {
        delay(100);
        // Check if WiFi is connected (provisioning successful)
        if (WiFi.status() == WL_CONNECTED)
        {
            Serial.println("BluFi provisioning successful!");
            provisioningActive = false;
            wifiConnected = true;
            resetWiFiFailureCount(); // Reset failure count on successful connection
            return true;
        }
    }

    // Timeout reached
    if (provisioningActive)
    {
        Serial.println("BluFi provisioning timeout!");
        provisioningActive = false;
        return false;
    }

    return false;
}

bool ConnectivityManager::connectWiFi(uint32_t timeoutMs)
{
    if (wifiConnected)
    {
        return true;
    }

    // Try to connect using stored credentials
    WiFi.mode(WIFI_STA);
    WiFi.begin(); // Use stored credentials

    Serial.print("Connecting to WiFi using stored credentials");

    uint32_t startTime = millis();
    while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < timeoutMs)
    {
        delay(WIFI_RETRY_INTERVAL);
        Serial.print(".");
    }

    if (WiFi.status() == WL_CONNECTED)
    {
        wifiConnected = true;
        onWiFiConnected();
        resetWiFiFailureCount(); // Reset failure count on successful connection
        return true;
    }
    else
    {
        Serial.println("\nWiFi connection failed!");
        incrementWiFiFailureCount();
        return false;
    }
}

bool ConnectivityManager::connectMQTT(uint32_t timeoutMs)
{
    if (!wifiConnected)
    {
        Serial.println("WiFi not connected, cannot connect to MQTT");
        return false;
    }

    if (mqttConnected && mqttClient.connected())
    {
        return true;
    }

    Serial.printf("Connecting to MQTT broker: %s:%d (TLS: %s)", mqttServer.c_str(), mqttPort, useTLS ? "enabled" : "disabled");

    uint32_t startTime = millis();
    while (!mqttClient.connected() && (millis() - startTime) < timeoutMs)
    {
        bool connected;

        if (mqttUser.length() > 0)
        {
            connected = mqttClient.connect(clientId.c_str(), mqttUser.c_str(), mqttPassword.c_str());
        }
        else
        {
            connected = mqttClient.connect(clientId.c_str());
        }

        if (connected)
        {
            mqttConnected = true;
            Serial.println("\nMQTT connected successfully!");
            if (useTLS)
            {
                Serial.println("Connection secured with TLS");
            }

            // Publish connection status
            // publishStatus(0, "connected");

            return true;
        }
        else
        {
            Serial.printf(".");
            delay(MQTT_RETRY_INTERVAL);
        }
    }

    Serial.printf("\nMQTT connection failed! State: %d\n", mqttClient.state());
    return false;
}

void ConnectivityManager::disconnect()
{
    if (mqttConnected)
    {
        mqttClient.disconnect();
        mqttConnected = false;
    }

    if (wifiConnected)
    {
        WiFi.disconnect();
        wifiConnected = false;
    }

    Serial.println("Disconnected from WiFi and MQTT");
}

bool ConnectivityManager::isConnected()
{
    return wifiConnected && mqttConnected && mqttClient.connected();
}

bool ConnectivityManager::publishSensorData(const SensorReading &reading, bool isDailyReport)
{
    if (!isConnected())
    {
        Serial.println("Not connected, cannot publish sensor data");
        return false;
    }

    String payload = createSensorDataPayload(reading, isDailyReport);

    bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), true); // retained message

    if (success)
    {
        Serial.printf("Published sensor data: %s\n", payload.c_str());
    }
    else
    {
        Serial.println("Failed to publish sensor data");
    }

    return success;
}

bool ConnectivityManager::publishStatus(uint32_t bootCount, const char *status)
{
    if (!isConnected())
    {
        return false;
    }

    String payload = createStatusPayload(bootCount, status);

    bool success = mqttClient.publish(topicStatus.c_str(), payload.c_str());

    if (success)
    {
        Serial.printf("Published status: %s\n", payload.c_str());
    }

    return success;
}

void ConnectivityManager::loop()
{
    if (mqttConnected)
    {
        mqttClient.loop();
    }
}

String ConnectivityManager::getConnectionStatus()
{
    String status = "WiFi: ";
    status += wifiConnected ? "Connected" : "Disconnected";
    status += ", MQTT: ";
    status += mqttConnected ? "Connected" : "Disconnected";
    status += ", Failures: " + String(wifiFailureCount);

    if (wifiConnected)
    {
        status += " (IP: " + WiFi.localIP().toString() + ")";
    }

    return status;
}

bool ConnectivityManager::hasStoredCredentials()
{
    // Check if WiFi credentials are stored in NVS
    wifi_config_t conf;
    esp_wifi_get_config(WIFI_IF_STA, &conf);
    return (strlen((char *)conf.sta.ssid) > 0);
}

void ConnectivityManager::clearStoredCredentials()
{
    Serial.println("Clearing stored WiFi credentials...");
    WiFi.disconnect(true, true); // Disconnect and erase stored credentials
    resetWiFiFailureCount();
}

uint8_t ConnectivityManager::getWiFiFailureCount()
{
    return wifiFailureCount;
}

void ConnectivityManager::resetWiFiFailureCount()
{
    wifiFailureCount = 0;
    saveWiFiFailureCount();
    Serial.println("WiFi failure count reset");
}

void ConnectivityManager::incrementWiFiFailureCount()
{
    wifiFailureCount++;
    saveWiFiFailureCount();
    Serial.printf("WiFi failure count incremented to: %d\n", wifiFailureCount);
}

void ConnectivityManager::loadWiFiFailureCount()
{
    wifiFailureCount = preferences.getUChar(PREF_WIFI_FAILURE_COUNT, 0);
}

void ConnectivityManager::saveWiFiFailureCount()
{
    preferences.putUChar(PREF_WIFI_FAILURE_COUNT, wifiFailureCount);
}

void ConnectivityManager::generateDeviceId()
{
    uint8_t mac[6];
    WiFi.macAddress(mac);

    deviceId = "fish-tank-sensor-1-";
    for (int i = 0; i < 6; i++)
    {
        if (mac[i] < 16)
            deviceId += "0";
        deviceId += String(mac[i], HEX);
    }

    clientId = deviceId + "-" + String(millis());
}

void ConnectivityManager::setupTopics()
{
    String baseTopic = "fishtank/" + deviceId;
    topicSensorData = baseTopic + "/sensor";
    topicStatus = baseTopic + "/status";
    topicHeartbeat = baseTopic + "/heartbeat";
}

void ConnectivityManager::mqttCallback(char *topic, byte *payload, unsigned int length)
{
    // For future use if we need to handle incoming MQTT messages
    Serial.printf("MQTT message received on topic: %s\n", topic);
}

String ConnectivityManager::createSensorDataPayload(const SensorReading &reading, bool isDailyReport)
{
    JsonDocument doc;

    doc["device_id"] = deviceId;
    doc["timestamp"] = reading.timestamp;
    doc["tds"] = reading.tds;
    doc["temperature"] = reading.temperature;
    doc["daily_report"] = isDailyReport;
    doc["uptime"] = millis();

    String payload;
    serializeJson(doc, payload);
    return payload;
}

String ConnectivityManager::createStatusPayload(uint32_t bootCount, const char *status)
{
    JsonDocument doc;

    doc["device_id"] = deviceId;
    doc["status"] = status;
    doc["boot_count"] = bootCount;
    doc["uptime"] = millis();
    doc["free_heap"] = ESP.getFreeHeap();
    doc["wifi_rssi"] = WiFi.RSSI();

    String payload;
    serializeJson(doc, payload);
    return payload;
}

void ConnectivityManager::onWiFiConnected()
{
    Serial.println();
    Serial.printf("WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("Signal strength: %d dBm\n", WiFi.RSSI());
}

void ConnectivityManager::onWiFiDisconnected()
{
    wifiConnected = false;
    mqttConnected = false;
    Serial.println("WiFi disconnected");
}

void ConnectivityManager::bluFiEventHandler(arduino_event_t *sys_event)
{
    switch (sys_event->event_id)
    {
    case ARDUINO_EVENT_WIFI_STA_GOT_IP:
        Serial.print("\nWiFi connected! IP address: ");
        Serial.println(IPAddress(sys_event->event_info.got_ip.ip_info.ip.addr));
        if (instance)
        {
            instance->wifiConnected = true;
        }
        break;

    case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
        Serial.println("\nWiFi disconnected");
        if (instance)
        {
            instance->wifiConnected = false;
            instance->mqttConnected = false;
        }
        break;

    case ARDUINO_EVENT_PROV_START:
        Serial.println("\nBluFi provisioning started");
        Serial.println("Use the ESP BLE Provisioning app to configure WiFi");
        break;

    case ARDUINO_EVENT_PROV_CRED_RECV:
        Serial.println("\nReceived WiFi credentials via BluFi");
        Serial.print("\tSSID: ");
        Serial.println((const char *)sys_event->event_info.prov_cred_recv.ssid);
        Serial.print("\tPassword: ");
        Serial.println((char const *)sys_event->event_info.prov_cred_recv.password);
        break;

    case ARDUINO_EVENT_PROV_CRED_FAIL:
        Serial.println("\nBluFi provisioning failed!");
        if (sys_event->event_info.prov_fail_reason == WIFI_PROV_STA_AUTH_ERROR)
        {
            Serial.println("WiFi password incorrect");
        }
        else
        {
            Serial.println("WiFi network not found");
        }
        if (instance)
        {
            instance->provisioningActive = false;
        }
        break;

    case ARDUINO_EVENT_PROV_CRED_SUCCESS:
        Serial.println("\nBluFi provisioning successful!");
        break;

    case ARDUINO_EVENT_PROV_END:
        Serial.println("\nBluFi provisioning ended");
        if (instance)
        {
            instance->provisioningActive = false;
        }
        break;

    default:
        break;
    }
}

void ConnectivityManager::setupTLS()
{
    Serial.println("Setting up TLS for secure MQTT connection...");

    // Custom CA certificate for EMQX SSL
    const char *ca_cert =
        "-----BEGIN CERTIFICATE-----\n"
        "MIIDrzCCApegAwIBAgIQCDvgVpBCRrGhdWrJWZHHSjANBgkqhkiG9w0BAQUFADBh\n"
        "MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3\n"
        "d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD\n"
        "QTAeFw0wNjExMTAwMDAwMDBaFw0zMTExMTAwMDAwMDBaMGExCzAJBgNVBAYTAlVT\n"
        "MRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j\n"
        "b20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENBMIIBIjANBgkqhkiG\n"
        "9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4jvhEXLeqKTTo1eqUKKPC3eQyaKl7hLOllsB\n"
        "CSDMAZOnTjC3U/dDxGkAV53ijSLdhwZAAIEJzs4bg7/fzTtxRuLWZscFs3YnFo97\n"
        "nh6Vfe63SKMI2tavegw5BmV/Sl0fvBf4q77uKNd0f3p4mVmFaG5cIzJLv07A6Fpt\n"
        "43C/dxC//AH2hdmoRBBYMql1GNXRor5H4idq9Joz+EkIYIvUX7Q6hL+hqkpMfT7P\n"
        "T19sdl6gSzeRntwi5m3OFBqOasv+zbMUZBfHWymeMr/y7vrTC0LUq7dBMtoM1O/4\n"
        "gdW7jVg/tRvoSSiicNoxBN33shbyTApOB6jtSj1etX+jkMOvJwIDAQABo2MwYTAO\n"
        "BgNVHQ8BAf8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUA95QNVbR\n"
        "TLtm8KPiGxvDl7I90VUwHwYDVR0jBBgwFoAUA95QNVbRTLtm8KPiGxvDl7I90VUw\n"
        "DQYJKoZIhvcNAQEFBQADggEBAMucN6pIExIK+t1EnE9SsPTfrgT1eXkIoyQY/Esr\n"
        "hMAtudXH/vTBH1jLuG2cenTnmCmrEbXjcKChzUyImZOMkXDiqw8cvpOp/2PV5Adg\n"
        "06O/nVsJ8dWO41P0jmP6P6fbtGbfYmbW0W5BjfIttep3Sp+dWOIrWcBAI+0tKIJF\n"
        "PnlUkiaY4IBIqDfv8NZ5YBberOgOzW6sRBc4L0na4UU+Krk2U886UAb3LujEV0ls\n"
        "YSEY1QSteDwsOoBrp+uvFRTp2InBuThs4pFsiv9kuXclVzDAGySj4dzp30d8tbQk\n"
        "CAUw7C29C79Fv1C5qfPrmAESrciIxpg0X40KPMbp1ZWVbd4=\n"
        "-----END CERTIFICATE-----\n";

    // Set the CA certificate for server verification
    wifiClientSecure.setCACert(ca_cert);

    // Set timeout for TLS handshake
    wifiClientSecure.setTimeout(10000); // 10 seconds

    // Enable SNI (Server Name Indication) for proper certificate validation
    wifiClientSecure.setHandshakeTimeout(30000); // 30 seconds for handshake

    Serial.println("TLS configuration completed with custom certificate");
}
