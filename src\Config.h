#ifndef CONFIG_H
#define CONFIG_H

// WiFi Configuration (now handled by BluFi provisioning)
// WiFi credentials will be configured via BluFi provisioning

// MQTT Configuration
#define MQTT_SERVER "192.168.10.67"
#define MQTT_PORT 18084

// Sensor Configuration
#define SENSOR_RX_PIN 8
#define SENSOR_TX_PIN 9
#define SENSOR_BAUDRATE 9600

// Power Management Configuration
#define SLEEP_MIN_SECONDS 1800   // Minimum sleep time in seconds
#define SLEEP_MAX_SECONDS 3600   // Maximum sleep time in seconds

// Reporting Thresholds
#define TDS_THRESHOLD_PPM 10  // TDS difference threshold in ppm
#define TEMP_THRESHOLD_C 0.5    // Temperature difference threshold in Celsius

// Connection Timeouts
#define WIFI_TIMEOUT_MS 30000   // WiFi connection timeout
#define MQTT_TIMEOUT_MS 10000   // MQTT connection timeout

// Debug Configuration
#define DEBUG_ENABLED true
#define SERIAL_BAUDRATE 115200

// Device Configuration
#define DEVICE_NAME "FishTankSensor"
#define FIRMWARE_VERSION "1.0.0"

// Timing Configuration
#define SENSOR_WARMUP_DELAY_MS 1000

// Memory Management
#define ENABLE_MEMORY_MONITORING false

#endif // CONFIG_H
