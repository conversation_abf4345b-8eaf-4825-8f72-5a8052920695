#include <unity.h>
#include "ConnectivityManager.h"

// Test TLS detection based on port
void test_tls_detection() {
    // Test with non-TLS port
    {
        ConnectivityManager connectivity;
        connectivity.begin("test.mqtt.server", 1883);
        
        // Get connection status to check TLS status
        String status = connectivity.getConnectionStatus();
        TEST_ASSERT_TRUE(status.indexOf("TLS: disabled") >= 0);
    }
    
    // Test with TLS port
    {
        ConnectivityManager connectivity;
        connectivity.begin("test.mqtt.server", 8883);
        
        // Get connection status to check TLS status
        String status = connectivity.getConnectionStatus();
        TEST_ASSERT_TRUE(status.indexOf("TLS: enabled") >= 0);
    }
}

void setup() {
    delay(2000);
    UNITY_BEGIN();
    
    RUN_TEST(test_tls_detection);
    
    UNITY_END();
}

void loop() {
    // Nothing to do here
}
