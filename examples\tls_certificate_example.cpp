/*
 * TLS Certificate Example for Fish Tank Sensor
 * 
 * 演示如何使用自定义证书格式进行TLS连接
 * 当MQTT端口为8883时，系统会自动启用TLS并使用指定的证书
 */

#include <Arduino.h>
#include "ConnectivityManager.h"
#include "Config.h"

ConnectivityManager connectivity;

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== TLS Certificate Example ===");
    Serial.println("演示端口8883时自动启用TLS配置");
    Serial.println();
    
    // 初始化连接管理器 - 端口8883会自动启用TLS
    connectivity.begin(MQTT_SERVER, MQTT_PORT);
    
    Serial.printf("MQTT服务器: %s\n", MQTT_SERVER);
    Serial.printf("MQTT端口: %d\n", MQTT_PORT);
    
    if (MQTT_PORT == 8883) {
        Serial.println("检测到端口8883 - TLS已自动启用");
        Serial.println("使用自定义证书格式进行安全连接");
    } else {
        Serial.println("使用标准MQTT连接（无TLS）");
    }
    
    Serial.println();
    
    // 处理WiFi连接
    if (!handleWiFiConnection()) {
        Serial.println("WiFi连接失败！");
        Serial.println("进入深度睡眠模式...");
        esp_deep_sleep_start();
        return;
    }
    
    // 连接到MQTT代理
    Serial.println("尝试连接MQTT代理...");
    if (connectivity.connectMQTT(30000)) {
        Serial.println("MQTT连接成功！");
        
        // 显示连接状态
        Serial.println("\n=== 连接状态 ===");
        Serial.println(connectivity.getConnectionStatus());
        
        // 测试发布消息
        Serial.println("\n测试安全消息发布...");
        if (connectivity.publishStatus(1, "TLS连接测试成功")) {
            Serial.println("消息通过TLS安全发布成功！");
        } else {
            Serial.println("消息发布失败");
        }
    } else {
        Serial.println("MQTT连接失败！");
    }
}

void loop() {
    // 保持MQTT连接活跃
    connectivity.loop();
    
    // 每30秒打印一次连接状态
    static unsigned long lastStatusPrint = 0;
    if (millis() - lastStatusPrint > 30000) {
        Serial.println("\n=== 当前状态 ===");
        Serial.println(connectivity.getConnectionStatus());
        lastStatusPrint = millis();
    }
    
    delay(1000);
}

bool handleWiFiConnection() {
    // 检查是否有存储的WiFi凭据
    if (!connectivity.hasStoredCredentials()) {
        Serial.println("未找到存储的WiFi凭据");
        Serial.println("启动BluFi配网...");
        Serial.println("请使用ESP BLE Provisioning应用配置WiFi");
        
        // 启动BluFi配网（3分钟超时）
        if (!connectivity.startBluFiProvisioning(180000)) {
            Serial.println("BluFi配网失败或超时");
            return false;
        }
        
        Serial.println("BluFi配网成功完成");
        return true;
    } else {
        Serial.println("找到存储的WiFi凭据，尝试连接...");
        
        // 尝试使用存储的凭据连接
        if (connectivity.connectWiFi(30000)) {
            Serial.println("WiFi连接成功");
            return true;
        } else {
            Serial.println("使用存储凭据的WiFi连接失败");
            return false;
        }
    }
}
