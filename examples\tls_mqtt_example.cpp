/*
 * TLS MQTT Example for Fish Tank Sensor
 * 
 * This example demonstrates how the ConnectivityManager automatically
 * configures TLS when using port 8883 (standard MQTT over TLS port).
 * 
 * Features demonstrated:
 * - Automatic TLS detection based on port number
 * - Secure MQTT connection using ESP32's built-in certificate bundle
 * - BluFi provisioning for WiFi configuration
 * - Connection status reporting with TLS information
 */

#include <Arduino.h>
#include "ConnectivityManager.h"
#include "Config.h"

ConnectivityManager connectivity;

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== TLS MQTT Example ===");
    Serial.println("This example demonstrates automatic TLS configuration");
    Serial.println("when using port 8883 for MQTT connections.\n");
    
    // Initialize connectivity manager
    // Port 8883 will automatically enable TLS
    connectivity.begin(MQTT_SERVER, MQTT_PORT);
    
    Serial.println("Connectivity manager initialized");
    Serial.println("TLS will be automatically enabled for port 8883");
    Serial.println();
    
    // Handle WiFi connection
    if (!handleWiFiConnection()) {
        Serial.println("WiFi connection failed!");
        Serial.println("Entering deep sleep...");
        esp_deep_sleep_start();
        return;
    }
    
    // Connect to MQTT broker with TLS
    Serial.println("Attempting MQTT connection...");
    if (connectivity.connectMQTT(30000)) {
        Serial.println("MQTT connection successful!");
        
        // Display connection status
        Serial.println("\n=== Connection Status ===");
        Serial.println(connectivity.getConnectionStatus());
        
        // Test publishing a message
        Serial.println("\nTesting secure message publishing...");
        if (connectivity.publishStatus(1, "TLS test successful")) {
            Serial.println("Message published successfully over TLS!");
        } else {
            Serial.println("Failed to publish message");
        }
    } else {
        Serial.println("MQTT connection failed!");
    }
}

void loop() {
    // Keep MQTT connection alive
    connectivity.loop();
    
    // Print connection status every 30 seconds
    static unsigned long lastStatusPrint = 0;
    if (millis() - lastStatusPrint > 30000) {
        Serial.println("\n=== Current Status ===");
        Serial.println(connectivity.getConnectionStatus());
        lastStatusPrint = millis();
    }
    
    delay(1000);
}

bool handleWiFiConnection() {
    // Check if we have stored WiFi credentials
    if (!connectivity.hasStoredCredentials()) {
        Serial.println("No stored WiFi credentials found");
        Serial.println("Starting BluFi provisioning...");
        Serial.println("Use the ESP BLE Provisioning app to configure WiFi");
        
        // Start BluFi provisioning (3 minutes timeout)
        if (!connectivity.startBluFiProvisioning(180000)) {
            Serial.println("BluFi provisioning failed or timed out");
            return false;
        }
        
        Serial.println("BluFi provisioning completed successfully");
        return true;
    } else {
        Serial.println("Found stored WiFi credentials, attempting connection...");
        
        // Try to connect using stored credentials
        if (connectivity.connectWiFi(30000)) {
            Serial.println("WiFi connection successful");
            return true;
        } else {
            Serial.println("WiFi connection failed with stored credentials");
            return false;
        }
    }
}
